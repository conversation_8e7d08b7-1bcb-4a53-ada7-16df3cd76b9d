// APB监视器
class apb_monitor extends uvm_monitor;

    // 虚拟接口
    virtual apb_if vif;

    // 分析端口
    uvm_analysis_port #(apb_seq_item) item_collected_port;

    // UVM宏
    `uvm_component_utils(apb_monitor)

    // 构造函数
    function new(string name = "apb_monitor", uvm_component parent = null);
        super.new(name, parent);
        item_collected_port = new("item_collected_port", this);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        if (!uvm_config_db#(virtual apb_if)::get(this, "", "vif", vif)) begin
            `uvm_fatal("NOVIF", "Virtual interface must be set for apb_monitor")
        end
    endfunction

    // run_phase
    virtual task run_phase(uvm_phase phase);
        apb_seq_item trans;

        vif.wait_reset_release();

        forever begin
            trans = apb_seq_item::type_id::create("trans");
            collect_transaction(trans);
            item_collected_port.write(trans);
        end
    endtask

    // 收集事务
    virtual task collect_transaction(apb_seq_item trans);
        // 等待SETUP阶段
        @(vif.monitor_cb);
        while (!(vif.monitor_cb.psel && !vif.monitor_cb.penable)) begin
            @(vif.monitor_cb);
        end

        // 采样SETUP阶段信号
        trans.pwrite = vif.monitor_cb.pwrite;
        trans.paddr  = vif.monitor_cb.paddr;
        trans.pwdata = vif.monitor_cb.pwdata;
        trans.pstrb  = vif.monitor_cb.pstrb;

        // 等待ACCESS阶段
        @(vif.monitor_cb);
        while (!(vif.monitor_cb.psel && vif.monitor_cb.penable && vif.monitor_cb.pready)) begin
            @(vif.monitor_cb);
        end

        // 采样ACCESS阶段信号
        trans.prdata  = vif.monitor_cb.prdata;
        trans.pready  = vif.monitor_cb.pready;
        trans.pslverr = vif.monitor_cb.pslverr;

        `uvm_info("APB_MONITOR", $sformatf("Transaction collected: %s", trans.convert2string()), UVM_MEDIUM)
    endtask

endclass
