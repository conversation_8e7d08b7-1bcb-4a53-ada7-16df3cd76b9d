// APB基础测试
class apb_base_test extends uvm_test;

    // 环境
    apb_env env;

    // UVM宏
    `uvm_component_utils(apb_base_test)

    // 构造函数
    function new(string name = "apb_base_test", uvm_component parent = null);
        super.new(name, parent);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);

        // 创建环境
        env = apb_env::type_id::create("env", this);

        // 设置默认序列
        uvm_config_db#(uvm_object_wrapper)::set(this, "env.agent.sequencer.main_phase",
                                                "default_sequence", apb_write_seq::type_id::get());
    endfunction

    // end_of_elaboration_phase
    virtual function void end_of_elaboration_phase(uvm_phase phase);
        super.end_of_elaboration_phase(phase);
        uvm_top.print_topology();
    endfunction

    // start_of_simulation_phase
    virtual function void start_of_simulation_phase(uvm_phase phase);
        super.start_of_simulation_phase(phase);
        `uvm_info("APB_BASE_TEST", "Starting APB Base Test", UVM_LOW)
    endfunction

endclass
