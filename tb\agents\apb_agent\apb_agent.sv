// APB代理
class apb_agent extends uvm_agent;

    // 组件
    apb_driver    driver;
    apb_monitor   monitor;
    apb_sequencer sequencer;

    // 分析端口
    uvm_analysis_port #(apb_seq_item) ap;

    // UVM宏
    `uvm_component_utils(apb_agent)

    // 构造函数
    function new(string name = "apb_agent", uvm_component parent = null);
        super.new(name, parent);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);

        // 创建monitor
        monitor = apb_monitor::type_id::create("monitor", this);

        // 如果是active agent，创建driver和sequencer
        if (get_is_active() == UVM_ACTIVE) begin
            driver = apb_driver::type_id::create("driver", this);
            sequencer = apb_sequencer::type_id::create("sequencer", this);
        end
    endfunction

    // connect_phase
    virtual function void connect_phase(uvm_phase phase);
        super.connect_phase(phase);

        // 连接分析端口
        ap = monitor.item_collected_port;

        // 如果是active agent，连接driver和sequencer
        if (get_is_active() == UVM_ACTIVE) begin
            driver.seq_item_port.connect(sequencer.seq_item_export);
        end
    endfunction

endclass
