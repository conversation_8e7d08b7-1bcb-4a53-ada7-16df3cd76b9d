// APB UVM包文件
package apb_pkg;

    import uvm_pkg::*;
    `include "uvm_macros.svh"

    // 包含所有文件
    `include "apb_seq_item.sv"
    `include "apb_sequencer.sv"
    `include "apb_driver.sv"
    `include "apb_monitor.sv"
    `include "apb_agent.sv"

    // 包含序列文件
    `include "apb_base_seq.sv"
    `include "apb_write_seq.sv"
    `include "apb_read_seq.sv"
    `include "apb_read_write_seq.sv"

    // 包含环境文件
    `include "apb_scoreboard.sv"
    `include "apb_env.sv"

    // 包含测试文件
    `include "apb_base_test.sv"
    `include "apb_read_write_test.sv"

endpackage
