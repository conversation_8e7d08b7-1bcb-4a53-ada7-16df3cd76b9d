// APB序列项定义
class apb_seq_item extends uvm_sequence_item;

    // 数据成员
    rand bit        pwrite;     // 读写控制：1-写，0-读
    rand bit [31:0] paddr;      // 地址
    rand bit [31:0] pwdata;     // 写数据
    rand bit [3:0]  pstrb;      // 字节选通
    bit [31:0]      prdata;     // 读数据
    bit             pready;     // 准备信号
    bit             pslverr;    // 错误信号

    // 约束
    constraint addr_c {
        paddr[31:4] == 28'h0;   // 只使用低4位地址
        paddr[1:0] == 2'b00;    // 字对齐
    }

    constraint strb_c {
        pstrb != 4'h0;          // 至少有一个字节使能
    }

    // UVM宏
    `uvm_object_utils_begin(apb_seq_item)
        `uvm_field_int(pwrite, UVM_ALL_ON)
        `uvm_field_int(paddr,  UVM_ALL_ON)
        `uvm_field_int(pwdata, UVM_ALL_ON)
        `uvm_field_int(pstrb,  UVM_ALL_ON)
        `uvm_field_int(prdata, UVM_ALL_ON)
        `uvm_field_int(pready, UVM_ALL_ON)
        `uvm_field_int(pslverr, UVM_ALL_ON)
    `uvm_object_utils_end

    // 构造函数
    function new(string name = "apb_seq_item");
        super.new(name);
    endfunction

    // 转换为字符串
    function string convert2string();
        string s;
        s = $sformatf("pwrite=%0b, paddr=0x%08h, pwdata=0x%08h, pstrb=0x%01h, prdata=0x%08h, pready=%0b, pslverr=%0b",
                      pwrite, paddr, pwdata, pstrb, prdata, pready, pslverr);
        return s;
    endfunction

endclass
