// APB测试平台顶层
module tb_top;

    import uvm_pkg::*;
    import apb_pkg::*;
    `include "uvm_macros.svh"

    // 时钟和复位信号
    logic pclk;
    logic presetn;

    // APB接口实例
    apb_if apb_vif(pclk, presetn);

    // DUT实例
    apb_slave dut (
        .pclk    (pclk),
        .presetn (presetn),
        .psel    (apb_vif.psel),
        .penable (apb_vif.penable),
        .pwrite  (apb_vif.pwrite),
        .paddr   (apb_vif.paddr),
        .pwdata  (apb_vif.pwdata),
        .pstrb   (apb_vif.pstrb),
        .prdata  (apb_vif.prdata),
        .pready  (apb_vif.pready),
        .pslverr (apb_vif.pslverr)
    );

    // 时钟生成
    initial begin
        pclk = 0;
        $display("Clock generation started at time %0t", $time);
        forever #5 pclk = ~pclk;  // 100MHz时钟
    end

    // 复位生成
    initial begin
        presetn = 0;
        $display("Reset asserted at time %0t", $time);
        #100;
        presetn = 1;
        $display("Reset released at time %0t", $time);
    end

    // 时钟监控
    always @(posedge pclk) begin
        if ($time < 200) begin
            $display("Clock edge at time %0t, pclk=%b, presetn=%b", $time, pclk, presetn);
        end
    end

    // 波形dump
    initial begin
        // 等待一点时间再开始dump，确保所有信号都已初始化
        #1;

        `ifdef DUMP_FSDB
            $fsdbDumpfile("tb_top.fsdb");
            $fsdbDumpvars(0, tb_top);
            $fsdbDumpMDA(0, tb_top);  // dump多维数组
            $fsdbDumpSVA(0, tb_top);  // dump SystemVerilog断言
            $display("FSDB dump started at time %0t", $time);
        `endif

        `ifdef DUMP_VCD
            $dumpfile("tb_top.vcd");
            $dumpvars(0, tb_top);
            $display("VCD dump started at time %0t", $time);
        `endif

        // 显式dump关键信号
        `ifdef DUMP_FSDB
            $fsdbDumpvars(0, pclk);
            $fsdbDumpvars(0, presetn);
            $fsdbDumpvars(0, apb_vif);
            $fsdbDumpvars(0, dut);
        `endif
    end

    // UVM配置和启动
    initial begin
        // 设置虚拟接口
        uvm_config_db#(virtual apb_if)::set(null, "*", "vif", apb_vif);

        // 启动测试
        run_test();
    end

    // 超时保护
    initial begin
        #1000000;  // 1ms超时
        `uvm_fatal("TIMEOUT", "Test timeout!")
    end

endmodule
