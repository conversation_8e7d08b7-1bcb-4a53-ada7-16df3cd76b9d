// APB记分板
class apb_scoreboard extends uvm_scoreboard;

    // 分析端口
    uvm_analysis_imp #(apb_seq_item, apb_scoreboard) item_collected_export;

    // 参考模型 - 简单的寄存器数组
    bit [31:0] ref_regs [0:3];

    // 统计计数器
    int write_count;
    int read_count;
    int error_count;

    // UVM宏
    `uvm_component_utils(apb_scoreboard)

    // 构造函数
    function new(string name = "apb_scoreboard", uvm_component parent = null);
        super.new(name, parent);
        item_collected_export = new("item_collected_export", this);
    endfunction

    // build_phase
    virtual function void build_phase(uvm_phase phase);
        super.build_phase(phase);

        // 初始化参考寄存器
        for (int i = 0; i < 4; i++) begin
            ref_regs[i] = 32'h0;
        end

        write_count = 0;
        read_count = 0;
        error_count = 0;
    endfunction

    // write方法 - 接收monitor的事务
    virtual function void write(apb_seq_item trans);
        bit [1:0] reg_addr = trans.paddr[3:2];

        `uvm_info("APB_SCB", $sformatf("Received transaction: %s", trans.convert2string()), UVM_MEDIUM)

        if (trans.pwrite) begin
            // 写操作 - 更新参考模型
            write_count++;

            // 根据字节选通更新参考寄存器
            if (trans.pstrb[0]) ref_regs[reg_addr][7:0]   = trans.pwdata[7:0];
            if (trans.pstrb[1]) ref_regs[reg_addr][15:8]  = trans.pwdata[15:8];
            if (trans.pstrb[2]) ref_regs[reg_addr][23:16] = trans.pwdata[23:16];
            if (trans.pstrb[3]) ref_regs[reg_addr][31:24] = trans.pwdata[31:24];

            `uvm_info("APB_SCB", $sformatf("Write to addr=0x%08h, data=0x%08h, strb=0x%01h",
                      trans.paddr, trans.pwdata, trans.pstrb), UVM_MEDIUM)

        end else begin
            // 读操作 - 检查数据
            read_count++;

            if (trans.prdata !== ref_regs[reg_addr]) begin
                error_count++;
                `uvm_error("APB_SCB", $sformatf("Data mismatch! Addr=0x%08h, Expected=0x%08h, Actual=0x%08h",
                           trans.paddr, ref_regs[reg_addr], trans.prdata))
            end else begin
                `uvm_info("APB_SCB", $sformatf("Read check PASS! Addr=0x%08h, Data=0x%08h",
                          trans.paddr, trans.prdata), UVM_MEDIUM)
            end
        end
    endfunction

    // report_phase
    virtual function void report_phase(uvm_phase phase);
        super.report_phase(phase);

        `uvm_info("APB_SCB", "=== SCOREBOARD REPORT ===", UVM_LOW)
        `uvm_info("APB_SCB", $sformatf("Write transactions: %0d", write_count), UVM_LOW)
        `uvm_info("APB_SCB", $sformatf("Read transactions: %0d", read_count), UVM_LOW)
        `uvm_info("APB_SCB", $sformatf("Errors detected: %0d", error_count), UVM_LOW)

        if (error_count == 0) begin
            `uvm_info("APB_SCB", "TEST PASSED - No errors detected!", UVM_LOW)
        end else begin
            `uvm_error("APB_SCB", $sformatf("TEST FAILED - %0d errors detected!", error_count))
        end
    endfunction

endclass
