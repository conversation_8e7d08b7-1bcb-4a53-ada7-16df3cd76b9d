// APB读序列
class apb_read_seq extends apb_base_seq;

    // UVM宏
    `uvm_object_utils(apb_read_seq)

    // 构造函数
    function new(string name = "apb_read_seq");
        super.new(name);
    endfunction

    // 主体任务
    virtual task body();
        apb_seq_item req;

        repeat(10) begin
            req = apb_seq_item::type_id::create("req");
            start_item(req);

            // 随机化读事务
            if (!req.randomize() with {
                pwrite == 1'b0;
            }) begin
                `uvm_error("APB_READ_SEQ", "Randomization failed")
            end

            `uvm_info("APB_READ_SEQ", $sformatf("Sending read: %s", req.convert2string()), UVM_MEDIUM)
            finish_item(req);
        end
    endtask

endclass
