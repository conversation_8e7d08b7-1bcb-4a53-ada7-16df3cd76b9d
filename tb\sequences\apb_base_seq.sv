// APB基础序列
class apb_base_seq extends uvm_sequence #(apb_seq_item);

    // UVM宏
    `uvm_object_utils(apb_base_seq)

    // 构造函数
    function new(string name = "apb_base_seq");
        super.new(name);
    endfunction

    // 预处理任务
    virtual task pre_body();
        if (starting_phase != null) begin
            starting_phase.raise_objection(this);
        end
    endtask

    // 后处理任务
    virtual task post_body();
        if (starting_phase != null) begin
            starting_phase.drop_objection(this);
        end
    endtask

endclass
